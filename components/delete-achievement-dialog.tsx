'use client';

import * as React from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { LoaderIcon } from '@/components/icons';
import type { Achievement } from '@/hooks/use-achievements';
import { toast } from '@/components/toast';

interface DeleteAchievementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  achievement: Achievement | null;
  onSuccess: () => void;
}

export function DeleteAchievementDialog({
  open,
  onOpenChange,
  achievement,
  onSuccess,
}: DeleteAchievementDialogProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleDelete = async () => {
    if (!achievement) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/achievements/${achievement.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete achievement');
      }

      toast({
        type: 'success',
        description: 'Achievement deleted successfully!',
      });

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error('Error deleting achievement:', error);
      toast({
        type: 'error',
        description: 'Failed to delete achievement. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!achievement) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your
            achievement and remove it from our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="my-4 p-4 bg-muted/50 rounded-lg border">
          <div className="text-sm font-medium text-muted-foreground mb-2">
            Achievement to be deleted:
          </div>
          <div className="text-sm leading-relaxed">
            {achievement.achievement}
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isLoading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading && (
              <div className="mr-2">
                <LoaderIcon size={16} />
              </div>
            )}
            Delete Achievement
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
