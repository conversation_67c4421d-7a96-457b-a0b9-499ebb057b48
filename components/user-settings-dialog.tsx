"use client";

import * as React from "react";
import { useState } from "react";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { LoaderIcon } from "@/components/icons";
import { useUserSettings } from "@/hooks/use-user-settings";
import { toast } from "@/components/toast";

interface UserSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserSettingsDialog({
  open,
  onOpenChange,
}: UserSettingsDialogProps) {
  const {
    isLoadingSettings,
    userSettings,
    updateSettings,
    isUpdatingSettings,
    error,
  } = useUserSettings();

  const [formData, setFormData] = useState({
    firstName: "",
    jobRole: "",
    jobDescription: "",
    company: "",
  });

  // Update form data when user settings are loaded
  React.useEffect(() => {
    if (userSettings) {
      setFormData({
        firstName: userSettings.firstName || "",
        jobRole: userSettings.jobRole || "",
        jobDescription: userSettings.jobDescription || "",
        company: userSettings.company || "",
      });
    }
  }, [userSettings]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await updateSettings(formData);
      toast({
        type: "success",
        description: "Settings updated successfully!",
      });
    } catch (error) {
      toast({
        type: "error",
        description: "Failed to update settings. Please try again.",
      });
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        data-testid="user-settings-dialog"
        side="center"
        className="max-h-[80vh] overflow-y-auto"
      >
        <SheetHeader className="text-center">
          <SheetTitle data-testid="user-settings-title">
            User Settings
          </SheetTitle>
          <SheetDescription data-testid="user-settings-description">
            Manage your account settings and preferences.
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          {isLoadingSettings ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin">
                <LoaderIcon size={24} />
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  placeholder="Enter your first name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobRole">Job Role</Label>
                <Input
                  id="jobRole"
                  type="text"
                  value={formData.jobRole}
                  onChange={(e) => handleInputChange("jobRole", e.target.value)}
                  placeholder="Enter your job role"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange("company", e.target.value)}
                  placeholder="Enter your company name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobDescription">Job Description</Label>
                <Textarea
                  id="jobDescription"
                  value={formData.jobDescription}
                  onChange={(e) =>
                    handleInputChange("jobDescription", e.target.value)
                  }
                  placeholder="Describe your job responsibilities"
                  rows={4}
                />
              </div>

              <Button
                type="submit"
                disabled={isUpdatingSettings}
                className="w-full"
              >
                {isUpdatingSettings ? (
                  <>
                    <div className="mr-2 animate-spin">
                      <LoaderIcon size={16} />
                    </div>
                    Updating...
                  </>
                ) : (
                  "Save Settings"
                )}
              </Button>
            </form>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
