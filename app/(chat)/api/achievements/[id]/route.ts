import { auth } from "@/app/(auth)/auth";
import {
  deleteAchievementById,
  getAchievementById,
  updateAchievement,
} from "@/lib/db/queries";
import { ChatSDKError } from "@/lib/errors";

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
  const session = await auth();

  if (!session?.user) {
    return new ChatSDKError("unauthorized:api").toResponse();
  }

  const achievementId = parseInt(id);
  if (isNaN(achievementId)) {
    return new ChatSDKError(
      "bad_request:api",
      "Invalid achievement ID",
    ).toResponse();
  }

  try {
    // Check if achievement exists and belongs to user
    const existingAchievement = await getAchievementById({ id: achievementId });

    if (!existingAchievement) {
      return new ChatSDKError(
        "bad_request:api",
        "Achievement not found",
      ).toResponse();
    }

    if (existingAchievement.userId !== session.user.id) {
      return new ChatSDKError(
        "unauthorized:api",
        "You do not have permission to update this achievement",
      ).toResponse();
    }

    // Parse request body
    const { achievement, achievedAt } = await request.json();

    if (
      !achievement ||
      typeof achievement !== "string" ||
      !achievement.trim()
    ) {
      return new ChatSDKError(
        "bad_request:api",
        "Achievement text is required",
      ).toResponse();
    }

    if (!achievedAt) {
      return new ChatSDKError(
        "bad_request:api",
        "Achievement date is required",
      ).toResponse();
    }

    const achievedAtDate = new Date(achievedAt);
    if (isNaN(achievedAtDate.getTime())) {
      return new ChatSDKError(
        "bad_request:api",
        "Invalid achievement date",
      ).toResponse();
    }

    // Update the achievement
    const updatedAchievement = await updateAchievement({
      id: achievementId,
      achievement: achievement.trim(),
      achievedAt: achievedAtDate,
    });

    return Response.json({
      success: true,
      achievement: {
        id: updatedAchievement[0].id,
        achievement: updatedAchievement[0].achievement,
        achievementUnprocessed: updatedAchievement[0].achievementUnprocessed,
        createdAt: updatedAchievement[0].createdAt,
        achievedAt: updatedAchievement[0].achievedAt,
      },
    });
  } catch (error) {
    console.error("Failed to update achievement:", error);
    return new ChatSDKError(
      "bad_request:database",
      "Failed to update achievement",
    ).toResponse();
  }
}

export async function DELETE(
  _: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
  const session = await auth();

  if (!session?.user) {
    return new ChatSDKError("unauthorized:api").toResponse();
  }

  const achievementId = parseInt(id);
  if (isNaN(achievementId)) {
    return new ChatSDKError(
      "bad_request:api",
      "Invalid achievement ID",
    ).toResponse();
  }

  try {
    // Check if achievement exists and belongs to user
    const existingAchievement = await getAchievementById({ id: achievementId });

    if (!existingAchievement) {
      return new ChatSDKError(
        "bad_request:api",
        "Achievement not found",
      ).toResponse();
    }

    if (existingAchievement.userId !== session.user.id) {
      return new ChatSDKError(
        "unauthorized:api",
        "You do not have permission to delete this achievement",
      ).toResponse();
    }

    // Delete the achievement
    const deletedAchievement = await deleteAchievementById({
      id: achievementId,
    });

    return Response.json({
      success: true,
      message: "Achievement deleted successfully",
      deletedAchievement: {
        id: deletedAchievement.id,
        achievement: deletedAchievement.achievement,
        achievementUnprocessed: deletedAchievement.achievementUnprocessed,
        createdAt: deletedAchievement.createdAt,
        achievedAt: deletedAchievement.achievedAt,
      },
    });
  } catch (error) {
    console.error("Failed to delete achievement:", error);
    return new ChatSDKError(
      "bad_request:database",
      "Failed to delete achievement",
    ).toResponse();
  }
}
