'use client';

import { useEffect } from 'react';
import { useSWRConfig } from 'swr';
import { useDataStream } from '@/components/data-stream-provider';

/**
 * Hook to handle achievement cache invalidation based on data stream events
 * This ensures the sidebar achievements count updates in real-time when
 * achievements are created, updated, or deleted via AI tools
 */
export function useAchievementCacheInvalidation() {
  const { mutate } = useSWRConfig();
  const { dataStream } = useDataStream();

  useEffect(() => {
    if (!dataStream?.length) return;

    // Check for achievement-related events in the data stream
    const achievementEvents = dataStream.filter(
      (delta) =>
        delta.type === 'data-achievement-created' ||
        delta.type === 'data-achievement-updated' ||
        delta.type === 'data-achievement-deleted',
    );

    if (achievementEvents.length > 0) {
      // Invalidate the achievements cache to trigger a refetch
      mutate('/api/achievements');
    }
  }, [dataStream, mutate]);
}
