import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { getAchievementsByUserId } from '@/lib/db/queries';

interface ViewAchievementsProps {
  session: Session;
}

export const viewAchievements = ({ session }: ViewAchievementsProps) =>
  tool({
    description:
      'Retrieve all achievements for the current authenticated user. Returns an array of achievements with their IDs, processed text, and achievement dates.',
    inputSchema: z.object({}),
    execute: async () => {
      try {
        const achievements = await getAchievementsByUserId({
          userId: session.user.id,
        });

        return {
          success: true,
          achievements: achievements.map((achievement) => ({
            id: achievement.id,
            achievement: achievement.achievement,
            achievedAt: achievement.achievedAt,
          })),
          count: achievements.length,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Failed to retrieve achievements',
        };
      }
    },
  });
