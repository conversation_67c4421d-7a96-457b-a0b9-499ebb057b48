import { tool, type UIMessageStreamWriter } from 'ai';
import type { Session } from 'next-auth';
import { z } from 'zod';
import { getDocumentById } from '@/lib/db/queries';
import { documentHandlersByArtifactKind } from '@/lib/artifacts/server';
import type { ChatMessage } from '@/lib/types';

interface UpdateDocumentProps {
  session: Session;
  dataStream: UIMessageStreamWriter<ChatMessage>;
}

export const updateDocument = ({ session, dataStream }: UpdateDocumentProps) =>
  tool({
    description: `Update an existing document with specific changes based on the provided description.

IMPORTANT: Only call this tool when you have a CLEAR, SPECIFIC update request that is ready to be processed.

DO NOT call this tool if:
- You need to ask the user clarifying questions about what changes to make
- The update requirements are unclear, vague, or incomplete
- The description contains questions, placeholders, or incomplete information
- You're still gathering information about what modifications are needed
- The user is asking general questions about document editing capabilities

ONLY call this tool when:
- The user has explicitly requested specific changes to an existing document
- You have a clear document ID and know exactly which document to update
- The update description is complete and actionable
- The changes are well-defined and no further clarification is needed
- You understand exactly what modifications should be made`,
    inputSchema: z.object({
      id: z
        .string()
        .describe(
          'The unique ID of the document to update. Must be a valid document ID that exists in the system.',
        ),
      description: z
        .string()
        .min(5)
        .max(1000)
        .describe(
          'A clear, specific description of the changes that need to be made to the document. Must be actionable and complete. Should NOT contain questions, placeholders, or vague requests.',
        ),
    }),
    execute: async ({ id, description }) => {
      const document = await getDocumentById({ id });

      if (!document) {
        return {
          error: 'Document not found',
        };
      }

      dataStream.write({
        type: 'data-clear',
        data: null,
        transient: true,
      });

      const documentHandler = documentHandlersByArtifactKind.find(
        (documentHandlerByArtifactKind) =>
          documentHandlerByArtifactKind.kind === document.kind,
      );

      if (!documentHandler) {
        throw new Error(`No document handler found for kind: ${document.kind}`);
      }

      await documentHandler.onUpdateDocument({
        document,
        description,
        dataStream,
        session,
      });

      dataStream.write({ type: 'data-finish', data: null, transient: true });

      return {
        id,
        title: document.title,
        kind: document.kind,
        content: 'The document has been updated successfully.',
      };
    },
  });
