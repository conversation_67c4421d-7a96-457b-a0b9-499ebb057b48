import { generateUUID } from "@/lib/utils";
import { tool, type UIMessageStreamWriter } from "ai";
import { z } from "zod";
import type { Session } from "next-auth";
import {
  artifactKinds,
  documentHandlersByArtifactKind,
} from "@/lib/artifacts/server";
import type { ChatMessage } from "@/lib/types";

interface CreateDocumentProps {
  session: Session;
  dataStream: UIMessageStreamWriter<ChatMessage>;
}

export const createDocument = ({ session, dataStream }: CreateDocumentProps) =>
  tool({
    description: `Create a document for writing or content creation activities. This tool generates document content based on the title and type.

IMPORTANT: Only call this tool when you have a CLEAR, COMPLETE document creation request that is ready to be processed.

DO NOT call this tool if:
- You need to ask the user clarifying questions about what document to create
- The document purpose or requirements are unclear or incomplete
- The title contains placeholders, questions, or incomplete information
- You're still gathering information about the document type or content
- The user is asking general questions about document creation capabilities

ONLY call this tool when:
- The user has explicitly requested document creation with a clear purpose
- The document title is complete, descriptive, and ready for document generation
- The document type (text/image) is appropriate for the intended content
- The requirements are fully understood and no further clarification is needed
- You have enough context to generate meaningful content`,
    inputSchema: z.object({
      title: z
        .string()
        .min(3)
        .max(200)
        .describe(
          "A complete, descriptive title that clearly defines what document should be created. Must be specific and ready for content generation. Should NOT contain questions, placeholders, or incomplete information.",
        ),
      kind: z
        .enum(artifactKinds)
        .describe(
          "The type of document to create: 'text' for written content (reports, essays, proposals, etc.) or 'image' for visual content generation. Choose based on the user's specific request and intended output.",
        ),
    }),
    execute: async ({ title, kind }) => {
      const id = generateUUID();

      dataStream.write({
        type: "data-kind",
        data: kind,
        transient: true,
      });

      dataStream.write({
        type: "data-id",
        data: id,
        transient: true,
      });

      dataStream.write({
        type: "data-title",
        data: title,
        transient: true,
      });

      dataStream.write({
        type: "data-clear",
        data: null,
        transient: true,
      });

      const documentHandler = documentHandlersByArtifactKind.find(
        (documentHandlerByArtifactKind) =>
          documentHandlerByArtifactKind.kind === kind,
      );

      if (!documentHandler) {
        throw new Error(`No document handler found for kind: ${kind}`);
      }

      await documentHandler.onCreateDocument({
        id,
        title,
        dataStream,
        session,
      });

      dataStream.write({ type: "data-finish", data: null, transient: true });

      return {
        id,
        title,
        kind,
        content: "A document was created and is now visible to the user.",
      };
    },
  });
