import { tool, type UIMessageStreamWriter } from "ai";
import { z } from "zod";
import type { Session } from "next-auth";
import { createAchievement } from "@/lib/db/queries";
import type { ChatMessage } from "@/lib/types";

interface CreateAchievementProps {
  session: Session;
  dataStream?: UIMessageStreamWriter<ChatMessage>;
}

export const createAchievementTool = ({
  session,
  dataStream,
}: CreateAchievementProps) =>
  tool({
    description: `Create a new achievement record for the current authenticated user.

IMPORTANT: Only call this tool when you have a COMPLETE, PROPERLY FORMATTED achievement that is ready to be permanently stored.

DO NOT call this tool if:
- You need to ask the user clarifying questions
- The achievement description is incomplete or partial
- The text contains conversational elements, questions, or placeholders
- You're still gathering information about the achievement

ONLY call this tool when:
- The user has provided a complete achievement description
- You have enhanced the achievement text using the STAR method (Situation, Task, Action, Result)
- The achievement is fully formed and ready for permanent storage
- No further user input is required`,
    inputSchema: z.object({
      achievement: z
        .string()
        .min(10)
        .max(2000)
        .describe(
          "The complete, enhanced achievement text that is ready for permanent storage. Must be a fully-formed statement with specific details, metrics, and outcomes. Should NOT contain questions, placeholders, or incomplete information.",
        ),
      achievementUnprocessed: z
        .string()
        .min(1)
        .max(2000)
        .describe(
          "The original, unprocessed achievement text exactly as provided by the user, before any enhancement or formatting",
        ),
      achievedAt: z
        .string()
        .datetime()
        .optional()
        .describe(
          "ISO date string of when the achievement occurred (optional). If not provided, current date will be used.",
        ),
    }),
    execute: async ({ achievement, achievementUnprocessed, achievedAt }) => {
      console.log(
        "Creating achievement",
        achievement,
        achievementUnprocessed,
        achievedAt,
      );

      try {
        const achievedAtDate = achievedAt ? new Date(achievedAt) : new Date();

        await createAchievement({
          achievement,
          achievementUnprocessed,
          userId: session.user.id,
          achievedAt: achievedAtDate,
        });

        // Send cache invalidation event to client
        if (dataStream) {
          dataStream.write({
            type: "data-achievement-created",
            data: { userId: session.user.id },
            transient: true,
          });
        }

        return {
          success: true,
          message: "Achievement created successfully",
          achievement: {
            achievement,
            achievementUnprocessed,
            userId: session.user.id,
            achievedAt: achievedAtDate,
          },
        };
      } catch (error) {
        console.error(error);
        return {
          success: false,
          error: "Failed to create achievement",
        };
      }
    },
  });
