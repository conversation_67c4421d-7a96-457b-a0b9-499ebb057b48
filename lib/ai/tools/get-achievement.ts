import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { getAchievementById } from '@/lib/db/queries';

interface GetAchievementProps {
  session: Session;
}

export const getAchievement = ({ session }: GetAchievementProps) =>
  tool({
    description:
      'Retrieve a specific achievement by its ID. Only returns the achievement if it belongs to the current authenticated user.',
    inputSchema: z.object({
      id: z.number().describe('The unique ID of the achievement to retrieve'),
    }),
    execute: async ({ id }) => {
      try {
        const achievement = await getAchievementById({ id });

        if (!achievement) {
          return {
            success: false,
            error: 'Achievement not found',
          };
        }

        // Verify the achievement belongs to the current user
        if (achievement.userId !== session.user.id) {
          return {
            success: false,
            error: 'You do not have permission to access this achievement',
          };
        }

        return {
          success: true,
          achievement: {
            id: achievement.id,
            achievement: achievement.achievement,
            achievedAt: achievement.achievedAt,
            userId: achievement.userId,
          },
        };
      } catch (error) {
        return {
          success: false,
          error: 'Failed to retrieve achievement',
        };
      }
    },
  });
