import { tool, type UIMessageStreamWriter } from "ai";
import { z } from "zod";
import type { Session } from "next-auth";
import { getAchievementById, updateAchievement } from "@/lib/db/queries";
import type { ChatMessage } from "@/lib/types";

interface UpdateAchievementProps {
  session: Session;
  dataStream?: UIMessageStreamWriter<ChatMessage>;
}

export const updateAchievementTool = ({
  session,
  dataStream,
}: UpdateAchievementProps) =>
  tool({
    description: `Update an existing achievement record. Only allows updating achievements that belong to the current authenticated user.

IMPORTANT: Only call this tool when you have COMPLETE, PROPERLY FORMATTED content that is ready to replace the existing achievement data.

DO NOT call this tool if:
- You need to ask the user clarifying questions about the update
- The updated achievement description is incomplete or partial
- The text contains conversational elements, questions, or placeholders
- You're still gathering information about what should be updated

ONLY call this tool when:
- The user has explicitly requested to update a specific achievement
- You have the complete, enhanced achievement text ready (if updating the achievement field)
- The updated content is fully formed and ready for permanent storage
- No further user input is required for the update`,
    inputSchema: z.object({
      id: z.number().describe("The unique ID of the achievement to update"),
      achievement: z
        .string()
        .min(10)
        .max(2000)
        .optional()
        .describe(
          "The complete, enhanced achievement text that will replace the existing achievement. Must be a fully-formed statement with specific details, metrics, and outcomes. Should NOT contain questions, placeholders, or incomplete information.",
        ),
      achievementUnprocessed: z
        .string()
        .min(1)
        .max(2000)
        .optional()
        .describe(
          "The updated original, unprocessed achievement text exactly as provided by the user, before any enhancement or formatting",
        ),
      achievedAt: z
        .string()
        .datetime()
        .optional()
        .describe(
          "ISO date string of when the achievement occurred. Provide only if the user wants to update the achievement date.",
        ),
    }),
    execute: async ({
      id,
      achievement,
      achievementUnprocessed,
      achievedAt,
    }) => {
      try {
        // First, verify the achievement exists and belongs to the user
        const existingAchievement = await getAchievementById({ id });

        if (!existingAchievement) {
          return {
            success: false,
            error: "Achievement not found",
          };
        }

        // Verify the achievement belongs to the current user
        if (existingAchievement.userId !== session.user.id) {
          return {
            success: false,
            error: "You do not have permission to update this achievement",
          };
        }

        // Check if at least one field is provided for update
        if (
          !achievement &&
          !achievementUnprocessed &&
          achievedAt === undefined
        ) {
          return {
            success: false,
            error:
              "At least one field (achievement, achievementUnprocessed, or achievedAt) must be provided for update",
          };
        }

        // Convert achievedAt string to Date if provided
        const achievedAtDate = achievedAt ? new Date(achievedAt) : undefined;

        // Update the achievement
        const [updatedAchievement] = await updateAchievement({
          id,
          achievement,
          achievementUnprocessed,
          achievedAt: achievedAtDate,
        });

        // Send cache invalidation event to client
        if (dataStream) {
          dataStream.write({
            type: "data-achievement-updated",
            data: { userId: session.user.id },
            transient: true,
          });
        }

        return {
          success: true,
          message: "Achievement updated successfully",
          achievement: {
            id: updatedAchievement.id,
            achievement: updatedAchievement.achievement,
            achievementUnprocessed: updatedAchievement.achievementUnprocessed,
            createdAt: updatedAchievement.createdAt,
            achievedAt: updatedAchievement.achievedAt,
            userId: updatedAchievement.userId,
          },
        };
      } catch (error) {
        return {
          success: false,
          error: "Failed to update achievement",
        };
      }
    },
  });
